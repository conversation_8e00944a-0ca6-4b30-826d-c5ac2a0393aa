import type { Metada<PERSON> } from 'next'
import { <PERSON> } from 'next/font/google'
import PerformanceMonitor from '@/components/performance/PerformanceMonitor'
import GoogleAnalytics from '@/components/analytics/GoogleAnalytics'
import SchemaMarkup from '@/components/seo/SchemaMarkup'
import CSSLoader from '@/components/optimization/CSSLoader'

// Optimize font loading with Next.js font optimization
const barlow = Barlow({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  display: 'swap', // Use font-display: swap for better LCP
  preload: true,
  variable: '--font-barlow',
})

export const metadata: Metadata = {
  title: {
    default: 'Navhaus | What matters, made real',
    template: 'Navhaus | %s'
  },
  description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
  keywords: [
    'navhaus',
    'custom wordpress website',
    'wordpress agency',
    'web development agency',
    'custom wordpress development',
    'wordpress website design',
    'wordpress web development',
    'custom web development',
    'wordpress specialists',
    'wordpress experts',
    'web design agency',
    'wordpress consulting'
  ],
  authors: [{ name: 'Navhaus' }],
  creator: 'Nav<PERSON>',
  publisher: 'Navhaus',
  metadataBase: new URL('https://navhaus.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Navhaus | What matters, made real',
    description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
    url: 'https://navhaus.com',
    siteName: 'Navhaus',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Navhaus - What matters, made real',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Navhaus | What matters, made real',
    description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
    images: ['/images/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  manifest: '/site.webmanifest',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={barlow.variable}>
      <head>
        {/* DNS prefetch for any external resources */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />

        {/* Preload critical CSS */}
        <link rel="preload" href="/styles/critical.css" as="style" />

        {/* Inline critical CSS for above-the-fold content */}
        <style dangerouslySetInnerHTML={{
          __html: `
            *,::before,::after{box-sizing:border-box;border:0 solid #e5e7eb}
            html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:var(--font-barlow),sans-serif;overflow-x:hidden}
            body{margin:0;line-height:inherit;color:#000;background-color:#f0ebde;overflow-x:hidden;font-family:var(--font-barlow),sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
            .text-hero{font-size:4rem;line-height:1.1;letter-spacing:-0.02em;font-weight:700;font-family:var(--font-barlow),sans-serif}
            .hero-section{min-height:100vh;display:flex;align-items:center;justify-content:center;padding:80px 2rem 2rem;position:relative}
            .nav-critical{position:fixed;top:0;left:0;right:0;z-index:50;background-color:#f0ebde;border-bottom:1px solid #e5e7eb;padding:0 2rem}
            .btn-primary{display:inline-block;padding:0.875rem 1.5rem;border:2px solid #000;background-color:transparent;color:#000;font-weight:700;text-transform:uppercase;letter-spacing:0.05em;text-decoration:none;border-radius:1rem;transition:all 0.2s ease;cursor:pointer;font-family:inherit}
            .btn-primary:hover{background-color:#000;color:#f0ebde}
            @media (max-width: 768px){.text-hero{font-size:2.5rem}.hero-section{padding:80px 1rem 2rem}}
            @media (max-width: 640px){.text-hero{font-size:2rem}}
          `
        }} />

        {/* Additional favicon declarations for better browser support */}
        <link rel="icon" type="image/svg+xml" href="/images/icon.svg" />
        <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="apple-mobile-web-app-title" content="Navhaus" />
        <meta name="theme-color" content="#434897" />
        <meta name="msapplication-TileColor" content="#434897" />

        <GoogleAnalytics />

        {/* Schema Markup for SEO */}
        <SchemaMarkup type="organization" />
        <SchemaMarkup type="localBusiness" />
      </head>
      <body className={barlow.className}>
        <PerformanceMonitor />
        {children}

        {/* Load non-critical CSS asynchronously */}
        <CSSLoader href="/styles/main.css" priority={false} />
        <CSSLoader href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css" priority={false} />
      </body>
    </html>
  )
}
