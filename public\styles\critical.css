
    *,::before,::after{box-sizing:border-box;border:0 solid #e5e7eb}
    html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:var(--font-barlow),sans-serif}
    body{margin:0;line-height:inherit;color:#000;background-color:#f0ebde;overflow-x:hidden}
  @font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:400;font-display:swap;src:url(/_next/static/media/8a63bc110e8f45ad-s.woff2) format("woff2");unicode-range:u+0102-0103,u+0110-0111,u+0128-0129,u+0168-0169,u+01a0-01a1,u+01af-01b0,u+0300-0301,u+0303-0304,u+0308-0309,u+0323,u+0329,u+1ea0-1ef9,u+20ab}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:400;font-display:swap;src:url(/_next/static/media/00045315ec24c208-s.woff2) format("woff2");unicode-range:u+0100-02ba,u+02bd-02c5,u+02c7-02cc,u+02ce-02d7,u+02dd-02ff,u+0304,u+0308,u+0329,u+1d00-1dbf,u+1e00-1e9f,u+1ef2-1eff,u+2020,u+20a0-20ab,u+20ad-20c0,u+2113,u+2c60-2c7f,u+a720-a7ff}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:400;font-display:swap;src:url(/_next/static/media/42ca9a2dc174b9b9-s.p.woff2) format("woff2");unicode-range:u+00??,u+0131,u+0152-0153,u+02bb-02bc,u+02c6,u+02da,u+02dc,u+0304,u+0308,u+0329,u+2000-206f,u+20ac,u+2122,u+2191,u+2193,u+2212,u+2215,u+feff,u+fffd}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:600;font-display:swap;src:url(/_next/static/media/ed0713aabc469750-s.woff2) format("woff2");unicode-range:u+0102-0103,u+0110-0111,u+0128-0129,u+0168-0169,u+01a0-01a1,u+01af-01b0,u+0300-0301,u+0303-0304,u+0308-0309,u+0323,u+0329,u+1ea0-1ef9,u+20ab}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:600;font-display:swap;src:url(/_next/static/media/ace9c6b312d37d07-s.woff2) format("woff2");unicode-range:u+0100-02ba,u+02bd-02c5,u+02c7-02cc,u+02ce-02d7,u+02dd-02ff,u+0304,u+0308,u+0329,u+1d00-1dbf,u+1e00-1e9f,u+1ef2-1eff,u+2020,u+20a0-20ab,u+20ad-20c0,u+2113,u+2c60-2c7f,u+a720-a7ff}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:600;font-display:swap;src:url(/_next/static/media/95a978e26cc29d74-s.p.woff2) format("woff2");unicode-range:u+00??,u+0131,u+0152-0153,u+02bb-02bc,u+02c6,u+02da,u+02dc,u+0304,u+0308,u+0329,u+2000-206f,u+20ac,u+2122,u+2191,u+2193,u+2212,u+2215,u+feff,u+fffd}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:700;font-display:swap;src:url(/_next/static/media/d4a6d1072ea531dd-s.woff2) format("woff2");unicode-range:u+0102-0103,u+0110-0111,u+0128-0129,u+0168-0169,u+01a0-01a1,u+01af-01b0,u+0300-0301,u+0303-0304,u+0308-0309,u+0323,u+0329,u+1ea0-1ef9,u+20ab}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:700;font-display:swap;src:url(/_next/static/media/e1012b8d4e21a3f0-s.woff2) format("woff2");unicode-range:u+0100-02ba,u+02bd-02c5,u+02c7-02cc,u+02ce-02d7,u+02dd-02ff,u+0304,u+0308,u+0329,u+1d00-1dbf,u+1e00-1e9f,u+1ef2-1eff,u+2020,u+20a0-20ab,u+20ad-20c0,u+2113,u+2c60-2c7f,u+a720-a7ff}
@font-face{font-family:__Barlow_1f456d;font-style:normal;font-weight:700;font-display:swap;src:url(/_next/static/media/f5e5067cd50e2c82-s.p.woff2) format("woff2");unicode-range:u+00??,u+0131,u+0152-0153,u+02bb-02bc,u+02c6,u+02da,u+02dc,u+0304,u+0308,u+0329,u+2000-206f,u+20ac,u+2122,u+2191,u+2193,u+2212,u+2215,u+feff,u+fffd}
@font-face{font-family:__Barlow_Fallback_1f456d;src:local("Arial");ascent-override:103.43%;descent-override:20.69%;line-gap-override:0.00%;size-adjust:96.68%}
--font-barlow:"__Barlow_1f456d","__Barlow_Fallback_1f456d"}*,:after,:before{--tw-border-spacing-x:0;
--tw-border-spacing-y:0;
--tw-translate-x:0;
--tw-translate-y:0;
--tw-rotate:0;
--tw-skew-x:0;
--tw-skew-y:0;
--tw-scale-x:1;
--tw-scale-y:1;
--tw-pan-x: ;
--tw-pan-y: ;
--tw-pinch-zoom: ;
--tw-scroll-snap-strictness:proximity;
--tw-gradient-from-position: ;
--tw-gradient-via-position: ;
--tw-gradient-to-position: ;
--tw-ordinal: ;
--tw-slashed-zero: ;
--tw-numeric-figure: ;
--tw-numeric-spacing: ;
--tw-numeric-fraction: ;
--tw-ring-inset: ;
--tw-ring-offset-width:0px;
--tw-ring-offset-color:#fff;
--tw-ring-color:rgba(59,130,246,.5);
--tw-ring-offset-shadow:0 0 #0000;
--tw-ring-shadow:0 0 #0000;
--tw-shadow:0 0 #0000;
--tw-shadow-colored:0 0 #0000;
--tw-contain-size: ;
--tw-contain-layout: ;
--tw-contain-paint: ;
--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;
--tw-contain-style: }/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/*,:after,:before{box-sizing:border-box;
--tw-content:""}:host,html{line-height:1.5;
--font-barlow),sans-serif;font-feature-settings:normal;
--font-barlow),sans-serif;overflow-x:hidden}body{--tw-bg-opacity:1;
--tw-bg-opacity,1));--tw-text-opacity:1;
--tw-text-opacity,1))}h1,h2,h3,h4,h5,h6{font-weight:700;
--font-barlow),sans-serif}article h1,aside h1,h1,nav h1,section h1{font-size:4rem!important}.text-hero{font-weight:700;
--font-barlow),sans-serif}@media (max-width:768px){.text-hero{font-size:2.5rem}}@media (max-width:640px){.text-hero{font-size:2rem}}.prose{color:var(--tw-prose-body);
--tw-prose-lead);font-size:1.25em;
--tw-prose-links);text-decoration:underline;
--tw-prose-bold);font-weight:600}.prose :where(a strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(blockquote strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(thead th strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:decimal;
--tw-prose-counters)}.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *))::marker{color:var(--tw-prose-bullets)}.prose :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);
--tw-prose-hr);border-top-width:1px;
--tw-prose-quotes);border-inline-start-width:.25rem;
--tw-prose-quote-borders);quotes:"\201C""\201D""\2018""\2019";
--tw-prose-headings);font-weight:800;
--tw-prose-headings);font-weight:700;
--tw-prose-headings);font-weight:600;
--tw-prose-kbd);box-shadow:0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%),0 3px 0 rgb(var(--tw-prose-kbd-shadows)/10%);
--tw-prose-code);font-weight:600;
--tw-prose-pre-code);background-color:var(--tw-prose-pre-bg);
--tw-prose-th-borders)}.prose :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);
--tw-prose-td-borders)}.prose :where(tbody tr:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:0}.prose :where(tbody td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:baseline}.prose :where(tfoot):not(:where([class~=not-prose],[class~=not-prose] *)){border-top-width:1px;
--tw-prose-th-borders)}.prose :where(tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:top}.prose :where(th,td):not(:where([class~=not-prose],[class~=not-prose] *)){text-align:start}.prose :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;
--tw-prose-captions);font-size:.875em;
--tw-prose-body:#374151;
--tw-prose-headings:#111827;
--tw-prose-lead:#4b5563;
--tw-prose-links:#111827;
--tw-prose-bold:#111827;
--tw-prose-counters:#6b7280;
--tw-prose-bullets:#d1d5db;
--tw-prose-hr:#e5e7eb;
--tw-prose-quotes:#111827;
--tw-prose-quote-borders:#e5e7eb;
--tw-prose-captions:#6b7280;
--tw-prose-kbd:#111827;
--tw-prose-kbd-shadows:17 24 39;
--tw-prose-code:#111827;
--tw-prose-pre-code:#e5e7eb;
--tw-prose-pre-bg:#1f2937;
--tw-prose-th-borders:#d1d5db;
--tw-prose-td-borders:#e5e7eb;
--tw-prose-invert-body:#d1d5db;
--tw-prose-invert-headings:#fff;
--tw-prose-invert-lead:#9ca3af;
--tw-prose-invert-links:#fff;
--tw-prose-invert-bold:#fff;
--tw-prose-invert-counters:#9ca3af;
--tw-prose-invert-bullets:#4b5563;
--tw-prose-invert-hr:#374151;
--tw-prose-invert-quotes:#f3f4f6;
--tw-prose-invert-quote-borders:#374151;
--tw-prose-invert-captions:#9ca3af;
--tw-prose-invert-kbd:#fff;
--tw-prose-invert-kbd-shadows:255 255 255;
--tw-prose-invert-code:#fff;
--tw-prose-invert-pre-code:#d1d5db;
--tw-prose-invert-pre-bg:rgba(0,0,0,.5);
--tw-prose-invert-th-borders:#4b5563;
--tw-prose-invert-td-borders:#374151;
--tw-border-opacity:1;
--tw-border-opacity,1));background-color:transparent;
--tw-text-opacity,1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;
--tw-bg-opacity,1));--tw-text-opacity:1}.btn-primary:hover,.btn-red{color:rgb(240 235 222/var(--tw-text-opacity,1))}.btn-red{border-radius:1rem;
--tw-border-opacity,1));--tw-bg-opacity:1;
--tw-bg-opacity,1));--tw-text-opacity:1}.btn-red:hover{background-color:transparent;
--tw-text-opacity,1))}.blog-content pre{position:relative}.blog-content pre code{display:block;
--tw-border-opacity,1));background-color:rgb(255 197 39/var(--tw-bg-opacity,1));
--tw-bg-opacity:0.2;
--tw-text-opacity,1))}.blog-content table{margin-top:2rem;
--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);
--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);
--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.blog-content table th{--tw-bg-opacity:1;
--tw-bg-opacity,1));font-weight:700;
--tw-text-opacity,1))}.blog-content table tr:nth-child(2n){--tw-bg-opacity:1;
--tw-bg-opacity,1))}.blog-content table tr:hover{--tw-bg-opacity:1;
--tw-text-opacity,1))}.blog-content blockquote{position:relative;
--tw-text-opacity,1))}.blog-content p+p{margin-top:1.5rem}.blog-content h2+p,.blog-content h3+p,.blog-content h4+p{margin-top:1rem}.blog-content a{transition-property:all;
--tw-translate-y:-1px;
--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.blog-content ul li::marker{--tw-text-opacity:1;
--tw-text-opacity,1))}.blog-content ol li::marker{font-weight:700;
--tw-text-opacity,1))}.blog-content img+em{margin-top:.5rem;
--tw-text-opacity,1))}.blog-content{line-height:1.7;
--tw-text-opacity,1));font-family:var(--font-barlow),serif}.blog-content a:focus{border-radius:.25rem;
--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);--tw-ring-color:rgb(67 72 151/var(--tw-ring-opacity,1));
--tw-ring-opacity:0.5}@media print{.blog-content,.blog-content a{--tw-text-opacity:1;
--tw-text-opacity,1))}.blog-content a{text-decoration-line:none}.blog-content a:after{content:" (" attr(href) ")";
--tw-text-opacity,1))}}.pointer-events-none{pointer-events:none}.visible{visibility:visible}.invisible{visibility:hidden}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0}.-bottom-2{bottom:-.5rem}.-bottom-4{bottom:-1rem}.-left-4{left:-1rem}.-left-6{left:-1.5rem}.-right-2{right:-.5rem}.-right-24{right:-6rem}.-right-4{right:-1rem}.-right-6{right:-1.5rem}.-right-8{right:-2rem}.-top-2{top:-.5rem}.-top-4{top:-1rem}.-top-6{top:-1.5rem}.-top-8{top:-2rem}.bottom-0{bottom:0}.bottom-1\/3{bottom:33.333333%}.bottom-1\/4{bottom:25%}.bottom-12{bottom:3rem}.bottom-16{bottom:4rem}.bottom-2{bottom:.5rem}.bottom-20{bottom:5rem}.bottom-24{bottom:6rem}.bottom-28{bottom:7rem}.bottom-32{bottom:8rem}.bottom-4{bottom:1rem}.bottom-6{bottom:1.5rem}.bottom-8{bottom:2rem}.left-0{left:0}.left-1\/2{left:50%}.left-1\/3{left:33.333333%}.left-1\/4{left:25%}.left-10{left:2.5rem}.left-12{left:3rem}.left-16{left:4rem}.left-2{left:.5rem}.left-20{left:5rem}.left-24{left:6rem}.left-32{left:8rem}.left-4{left:1rem}.left-6{left:1.5rem}.left-8{left:2rem}.right-0{right:0}.right-1\/3{right:33.333333%}.right-1\/4{right:25%}.right-12{right:3rem}.right-16{right:4rem}.right-2{right:.5rem}.right-20{right:5rem}.right-4{right:1rem}.right-6{right:1.5rem}.right-8{right:2rem}.top-0{top:0}.top-1\/2{top:50%}.top-1\/3{top:33.333333%}.top-1\/4{top:25%}.top-10{top:2.5rem}.top-12{top:3rem}.top-16{top:4rem}.top-2{top:.5rem}.top-20{top:5rem}.top-3\/4{top:75%}.top-32{top:8rem}.top-4{top:1rem}.top-40{top:10rem}.top-6{top:1.5rem}.top-8{top:2rem}.z-10{z-index:10}.z-50{z-index:50}.col-span-12{grid-column:span 12/span 12}.mx-auto{margin-left:auto;
--tw-translate-x:-50%}.-translate-x-1\/2,.-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\/2{--tw-translate-y:-50%}.rotate-45{--tw-rotate:45deg}.rotate-45,.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes pulse{50%{opacity:.5}}.animate-pulse{animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite}@keyframes spin{to{transform:rotate(1turn)}}.animate-spin{animation:spin 1s linear infinite}.resize-none{resize:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-12{gap:3rem}.gap-16{gap:4rem}.gap-2{gap:.5rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.space-x-1>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(.25rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-3>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-6>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.space-y-24>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(6rem * var(--tw-space-y-reverse))}.space-y-3>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-6>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-8>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}.overflow-hidden{overflow:hidden}.whitespace-nowrap{white-space:nowrap}.rounded{border-radius:.25rem}.rounded-3xl{border-radius:1.5rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:.5rem}.rounded-md{border-radius:.375rem}.rounded-xl{border-radius:.75rem}.rounded-b-full{border-bottom-right-radius:9999px}.rounded-b-full,.rounded-l-full{border-bottom-left-radius:9999px}.rounded-l-full{border-top-left-radius:9999px}.rounded-r-full{border-top-right-radius:9999px;
--tw-border-opacity,1))}.border-bauhaus-blue{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-bauhaus-red{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-bauhaus-yellow{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-black{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-brand-background{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-gray-300{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-green-400{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-red-400{--tw-border-opacity:1;
--tw-border-opacity,1))}.border-b-transparent{border-bottom-color:transparent}.border-l-transparent{border-left-color:transparent}.border-r-transparent{border-right-color:transparent}.border-t-transparent{border-top-color:transparent}.bg-bauhaus-black{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-bauhaus-blue{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-bauhaus-red{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-bauhaus-white{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-bauhaus-yellow{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-black{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-blue-600{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-brand-background{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-brand-blue{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-brand-red{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-brand-yellow{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-current{background-color:currentColor}.bg-gray-200{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-gray-800{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-green-100{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-red-100{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-transparent{background-color:transparent}.bg-white{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.bg-opacity-20{--tw-bg-opacity:0.2}.bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.from-bauhaus-blue{--tw-gradient-from:#434897 var(--tw-gradient-from-position);
--tw-gradient-to:rgba(67,72,151,0) var(--tw-gradient-to-position);
--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.to-bauhaus-red{--tw-gradient-to:#e94436 var(--tw-gradient-to-position)}.p-12{padding:3rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-3{padding-left:.75rem;
--tw-text-opacity,1))}.text-bauhaus-blue{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-bauhaus-red{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-bauhaus-white{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-bauhaus-yellow{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-black{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-blue-100{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-brand-background{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-300{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-400{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-500{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-600{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-700{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-800{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-gray-900{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-green-400{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-green-700{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-orange-400{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-red-100{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-red-400{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-red-700{--tw-text-opacity:1;
--tw-text-opacity,1))}.text-white{--tw-text-opacity:1;
--tw-text-opacity,1))}.opacity-0{opacity:0}.opacity-10{opacity:.1}.opacity-100{opacity:1}.opacity-15{opacity:.15}.opacity-20{opacity:.2}.opacity-25{opacity:.25}.opacity-30{opacity:.3}.opacity-40{opacity:.4}.opacity-5{opacity:.05}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-80{opacity:.8}.opacity-90{opacity:.9}.shadow-sm{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);
--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.blur{--tw-blur:blur(8px)}.blur,.invert{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.invert{--tw-invert:invert(100%)}.transition-all{transition-property:all;
--tw-bg-opacity,1))}.hover\:bg-bauhaus-blue:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-bauhaus-red:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-brand-background:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-gray-50:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-gray-900:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-red-700:hover{--tw-bg-opacity:1;
--tw-bg-opacity,1))}.hover\:bg-transparent:hover{background-color:transparent}.hover\:text-bauhaus-blue:hover{--tw-text-opacity:1;
--tw-text-opacity,1))}.hover\:text-bauhaus-red:hover{--tw-text-opacity:1;
--tw-text-opacity,1))}.hover\:text-bauhaus-yellow:hover{--tw-text-opacity:1;
--tw-text-opacity,1))}.hover\:text-brand-background:hover{--tw-text-opacity:1;
--tw-text-opacity,1))}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);
--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);
--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.focus\:border-bauhaus-blue:focus{--tw-border-opacity:1;
--tw-border-opacity,1))}.focus\:outline-none:focus{outline:2px solid transparent;
--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.prose-headings\:font-bold :is(:where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *))){font-weight:700}.prose-headings\:tracking-tight :is(:where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *))){letter-spacing:-.01em}.prose-headings\:text-bauhaus-black :is(:where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-h1\:mb-8 :is(:where(h1):not(:where([class~=not-prose],[class~=not-prose] *))){margin-bottom:2rem}.prose-h1\:mt-12 :is(:where(h1):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:3rem}.prose-h1\:text-4xl :is(:where(h1):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:2.25rem;
--tw-border-opacity,1))}.prose-h2\:pb-3 :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){padding-bottom:.75rem}.prose-h2\:text-3xl :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:1.875rem;
--tw-text-opacity,1))}.prose-a\:border-b :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))){border-bottom-width:1px}.prose-a\:border-bauhaus-blue :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-border-opacity:1;
--tw-border-opacity,1))}.prose-a\:font-medium :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))){font-weight:500}.prose-a\:text-bauhaus-blue :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-a\:no-underline :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))){text-decoration-line:none}.hover\:prose-a\:border-bauhaus-red :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))):hover{--tw-border-opacity:1;
--tw-border-opacity,1))}.hover\:prose-a\:text-bauhaus-red :is(:where(a):not(:where([class~=not-prose],[class~=not-prose] *))):hover{--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-blockquote\:ml-0 :is(:where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *))){margin-left:0}.prose-blockquote\:rounded-r-lg :is(:where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *))){border-top-right-radius:.5rem;
--tw-border-opacity,1))}.prose-blockquote\:bg-bauhaus-yellow :is(:where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-bg-opacity:1;
--tw-bg-opacity,1))}.prose-blockquote\:py-4 :is(:where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *))){padding-top:1rem;
--tw-text-opacity,1))}.prose-strong\:font-bold :is(:where(strong):not(:where([class~=not-prose],[class~=not-prose] *))){font-weight:700}.prose-strong\:text-bauhaus-black :is(:where(strong):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-em\:italic :is(:where(em):not(:where([class~=not-prose],[class~=not-prose] *))){font-style:italic}.prose-em\:text-bauhaus-black :is(:where(em):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-code\:rounded :is(:where(code):not(:where([class~=not-prose],[class~=not-prose] *))){border-radius:.25rem}.prose-code\:bg-bauhaus-yellow :is(:where(code):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-bg-opacity:1;
--tw-bg-opacity,1))}.prose-code\:px-2 :is(:where(code):not(:where([class~=not-prose],[class~=not-prose] *))){padding-left:.5rem;
--tw-text-opacity,1))}.prose-pre\:overflow-x-auto :is(:where(pre):not(:where([class~=not-prose],[class~=not-prose] *))){overflow-x:auto}.prose-pre\:rounded-xl :is(:where(pre):not(:where([class~=not-prose],[class~=not-prose] *))){border-radius:.75rem}.prose-pre\:bg-bauhaus-black :is(:where(pre):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-bg-opacity:1;
--tw-bg-opacity,1))}.prose-pre\:p-6 :is(:where(pre):not(:where([class~=not-prose],[class~=not-prose] *))){padding:1.5rem}.prose-pre\:text-bauhaus-white :is(:where(pre):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;
--tw-text-opacity,1))}.prose-ol\:my-6 :is(:where(ol):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:1.5rem;
--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.prose-ul\:my-6 :is(:where(ul):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:1.5rem;
--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.prose-li\:text-lg :is(:where(li):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:1.125rem;
--tw-text-opacity,1))}.prose-table\:border-collapse :is(:where(table):not(:where([class~=not-prose],[class~=not-prose] *))){border-collapse:collapse}.prose-table\:overflow-hidden :is(:where(table):not(:where([class~=not-prose],[class~=not-prose] *))){overflow:hidden}.prose-table\:rounded-lg :is(:where(table):not(:where([class~=not-prose],[class~=not-prose] *))){border-radius:.5rem}.prose-table\:border :is(:where(table):not(:where([class~=not-prose],[class~=not-prose] *))){border-width:1px}.prose-table\:border-bauhaus-black :is(:where(table):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-border-opacity:1;
--tw-border-opacity,1))}.prose-thead\:bg-bauhaus-blue :is(:where(thead):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-bg-opacity:1;
--tw-bg-opacity,1))}.prose-th\:border :is(:where(th):not(:where([class~=not-prose],[class~=not-prose] *))){border-width:1px}.prose-th\:border-bauhaus-black :is(:where(th):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-border-opacity:1;
--tw-border-opacity,1))}.prose-th\:px-4 :is(:where(th):not(:where([class~=not-prose],[class~=not-prose] *))){padding-left:1rem;
--tw-text-opacity,1))}.prose-td\:border :is(:where(td):not(:where([class~=not-prose],[class~=not-prose] *))){border-width:1px}.prose-td\:border-bauhaus-black :is(:where(td):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-border-opacity:1;
--tw-border-opacity,1))}.prose-td\:px-4 :is(:where(td):not(:where([class~=not-prose],[class~=not-prose] *))){padding-left:1rem;
--tw-text-opacity,1))}.prose-img\:rounded-xl :is(:where(img):not(:where([class~=not-prose],[class~=not-prose] *))){border-radius:.75rem}.prose-img\:shadow-sm :is(:where(img):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);
--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}@media (min-width:640px){.sm\:flex-row{flex-direction:row}.sm\:items-center{align-items:center}.sm\:space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;
--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.sm\:space-y-0>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}}@media (min-width:768px){.md\:col-span-1{grid-column:span 1/span 1}.md\:col-span-3{grid-column:span 3/span 3}.md\:col-span-4{grid-column:span 4/span 4}.md\:col-span-5{grid-column:span 5/span 5}.md\:col-span-7{grid-column:span 7/span 7}.md\:col-span-8{grid-column:span 8/span 8}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:h-16{height:4rem}.md\:w-8{width:2rem}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.md\:flex-row{flex-direction:row}.md\:space-y-0>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;
--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}.md\:p-12{padding:3rem}.md\:p-8{padding:2rem}.md\:px-12{padding-left:3rem;
*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }
*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/*,:after,:before{box-sizing:border-box;border:0 solid #e5e7eb}
*)){margin-top:1.25em;margin-bottom:1.25em}
*)){color:var(--tw-prose-lead);font-size:1.25em;line-height:1.6;margin-top:1.2em;margin-bottom:1.2em}
*)){color:var(--tw-prose-links);text-decoration:underline;font-weight:500}
*)){color:var(--tw-prose-bold);font-weight:600}
*)){list-style-type:decimal;margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em}
*)){list-style-type:upper-alpha}
*)){list-style-type:lower-alpha}
*)){list-style-type:upper-roman}
*)){list-style-type:lower-roman}
*)){list-style-type:decimal}
*)){list-style-type:disc;margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em}
*))::marker{font-weight:400;color:var(--tw-prose-counters)}
*)){color:var(--tw-prose-headings);font-weight:600;margin-top:1.25em}
*)){border-color:var(--tw-prose-hr);border-top-width:1px;margin-top:3em;margin-bottom:3em}
*)){font-weight:500;font-style:italic;color:var(--tw-prose-quotes);border-inline-start-width:.25rem;border-inline-start-color:var(--tw-prose-quote-borders);quotes:"\201C""\201D""\2018""\2019";margin-top:1.6em;margin-bottom:1.6em;padding-inline-start:1em}
*)):before{content:open-quote}
*)):after{content:close-quote}
*)){color:var(--tw-prose-headings);font-weight:800;font-size:2.25em;margin-top:0;margin-bottom:.8888889em;line-height:1.1111111}
*)){font-weight:900;color:inherit}
*)){color:var(--tw-prose-headings);font-weight:700;font-size:1.5em;margin-top:2em;margin-bottom:1em;line-height:1.3333333}
*)){font-weight:800;color:inherit}
*)){color:var(--tw-prose-headings);font-weight:600;font-size:1.25em;margin-top:1.6em;margin-bottom:.6em;line-height:1.6}
*)){font-weight:700;color:inherit}
*)){color:var(--tw-prose-headings);font-weight:600;margin-top:1.5em;margin-bottom:.5em;line-height:1.5}
*)){margin-top:2em;margin-bottom:2em}
*)){display:block;margin-top:2em;margin-bottom:2em}
*)){font-weight:500;font-family:inherit;color:var(--tw-prose-kbd);box-shadow:0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%),0 3px 0 rgb(var(--tw-prose-kbd-shadows)/10%);font-size:.875em;border-radius:.3125rem;padding-top:.1875em;padding-inline-end:.375em;padding-bottom:.1875em;padding-inline-start:.375em}
*)){color:var(--tw-prose-code);font-weight:600;font-size:.875em}
*)):before{content:"`"}
*)):after{content:"`"}
*)){color:inherit;font-size:.875em}
*)){color:inherit;font-size:.9em}
*)){color:var(--tw-prose-pre-code);background-color:var(--tw-prose-pre-bg);overflow-x:auto;font-weight:400;font-size:.875em;line-height:1.7142857;margin-top:1.7142857em;margin-bottom:1.7142857em;border-radius:.375rem;padding-top:.8571429em;padding-inline-end:1.1428571em;padding-bottom:.8571429em;padding-inline-start:1.1428571em}
*)){background-color:transparent;border-width:0;border-radius:0;padding:0;font-weight:inherit;color:inherit;font-size:inherit;font-family:inherit;line-height:inherit}
*)):before{content:none}
*)):after{content:none}
*)){width:100%;table-layout:auto;margin-top:2em;margin-bottom:2em;font-size:.875em;line-height:1.7142857}
*)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-th-borders)}
*)){color:var(--tw-prose-headings);font-weight:600;vertical-align:bottom;padding-inline-end:.5714286em;padding-bottom:.5714286em;padding-inline-start:.5714286em}
*)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-td-borders)}
*)){border-top-width:1px;border-top-color:var(--tw-prose-th-borders)}
*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}
*)){color:var(--tw-prose-captions);font-size:.875em;line-height:1.4285714;margin-top:.8571429em}
*)){margin-top:.5em;margin-bottom:.5em}
*)){padding-inline-start:.375em}
*)){margin-top:.75em;margin-bottom:.75em}
*)){margin-top:1.25em}
*)){margin-bottom:1.25em}
*)){margin-top:.5em;padding-inline-start:1.625em}
*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}
*)){padding-inline-start:0}
*)){padding-inline-end:0}
*)){padding-top:.5714286em;padding-inline-end:.5714286em;padding-bottom:.5714286em;padding-inline-start:.5714286em}
*)){margin-bottom:0}
*)){margin-top:1.2em;margin-bottom:1.2em}
*)){font-size:1.2em;line-height:1.5;margin-top:1em;margin-bottom:1em}
*)){margin-top:1.6em;margin-bottom:1.6em;padding-inline-start:1.0666667em}
*)){font-size:2.8em;margin-top:0;margin-bottom:.8571429em;line-height:1}
*)){font-size:1.8em;margin-top:1.5555556em;margin-bottom:.8888889em;line-height:1.1111111}
*)){font-size:1.5em;margin-top:1.6em;margin-bottom:.6666667em;line-height:1.3333333}
*)){margin-top:1.8em;margin-bottom:.6em;line-height:1.6}
*)){font-size:.9em;border-radius:.3125rem;padding-top:.25em;padding-inline-end:.4em;padding-bottom:.25em;padding-inline-start:.4em}
*)){font-size:.9em}
*)){font-size:.8611111em}
*)){font-size:.9em;line-height:1.7777778;margin-top:2em;margin-bottom:2em;border-radius:.5rem;padding-top:1.1111111em;padding-inline-end:1.3333333em;padding-bottom:1.1111111em;padding-inline-start:1.3333333em}
*)){margin-top:1.2em;margin-bottom:1.2em;padding-inline-start:1.6em}
*)){margin-top:.6em;margin-bottom:.6em}
*)){padding-inline-start:.4em}
*)){margin-top:.8em;margin-bottom:.8em}
*)){margin-top:1.2em}
*)){margin-bottom:1.2em}
*)){margin-top:.6em;padding-inline-start:1.6em}
*)){margin-top:2.8em;margin-bottom:2.8em}
*)){font-size:.9em;line-height:1.5555556}
*)){padding-inline-end:.6666667em;padding-bottom:.8888889em;padding-inline-start:.6666667em}
*)){padding-top:.8888889em;padding-inline-end:.6666667em;padding-bottom:.8888889em;padding-inline-start:.6666667em}
*)){font-size:.9em;line-height:1.5555556;margin-top:1em}
* var(--tw-space-x-reverse));margin-left:calc(.25rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}
* var(--tw-space-x-reverse));margin-left:calc(.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}
* var(--tw-space-x-reverse));margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}
* calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.space-y-24>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(6rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(6rem * var(--tw-space-y-reverse))}
* calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}
* calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-8>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}
*))){--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}
*))){font-size:2.25rem;line-height:2.5rem}
*))){line-height:1.25}
*))){margin-bottom:1.5rem}
*))){margin-top:2.5rem}
*))){--tw-border-opacity:1;border-color:rgb(0 0 0/var(--tw-border-opacity,1))}
*))){font-size:1.875rem;line-height:2.25rem}
*))){margin-bottom:1rem}
*))){margin-top:2rem}
*))){font-size:1.5rem;line-height:2rem}
*))){line-height:1.375}
*))){margin-bottom:.75rem}
*))){margin-top:1.5rem}
*))){font-size:1.25rem;line-height:1.75rem}
*))){font-size:1.125rem;line-height:1.75rem}
*))){line-height:1.625}
*))){--tw-border-opacity:1;border-color:rgb(67 72 151/var(--tw-border-opacity,1))}
*))){--tw-text-opacity:1;color:rgb(67 72 151/var(--tw-text-opacity,1))}
*))):hover{--tw-border-opacity:1;border-color:rgb(233 68 54/var(--tw-border-opacity,1))}
*))):hover{--tw-text-opacity:1;color:rgb(233 68 54/var(--tw-text-opacity,1))}
*))){border-top-right-radius:.5rem;border-bottom-right-radius:.5rem}
*))){border-left-width:4px}
*))){--tw-bg-opacity:1;background-color:rgb(255 197 39/var(--tw-bg-opacity,1))}
*))){padding-top:1rem;padding-bottom:1rem}
*))){padding-left:1.5rem}
*))){padding-left:.5rem;padding-right:.5rem}
*))){padding-top:.25rem;padding-bottom:.25rem}
*))){font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}
*))){font-size:.875rem;line-height:1.25rem}
*))){--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1))}
*))){--tw-text-opacity:1;color:rgb(240 235 222/var(--tw-text-opacity,1))}
*))){margin-top:1.5rem;margin-bottom:1.5rem}
*)))>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}
*))){--tw-bg-opacity:1;background-color:rgb(67 72 151/var(--tw-bg-opacity,1))}
*))){padding-left:1rem;padding-right:1rem}
*))){padding-top:.75rem;padding-bottom:.75rem}
*))){text-align:left}
*))){--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}
* var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.sm\:space-y-0>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}
* calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}.md\:p-12{padding:3rem}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:var(--font-barlow),sans-serif;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}
html{font-family:var(--font-barlow),sans-serif;overflow-x:hidden}
body{margin:0;line-height:inherit}
body,html{font-family:var(--font-barlow),sans-serif;overflow-x:hidden}
body{--tw-bg-opacity:1;background-color:rgb(240 235 222/var(--tw-bg-opacity,1));--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}
body);max-width:65ch}.prose :where(p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}
body tr):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-td-borders)}
body:#374151;--tw-prose-headings:#111827;--tw-prose-lead:#4b5563;--tw-prose-links:#111827;--tw-prose-bold:#111827;--tw-prose-counters:#6b7280;--tw-prose-bullets:#d1d5db;--tw-prose-hr:#e5e7eb;--tw-prose-quotes:#111827;--tw-prose-quote-borders:#e5e7eb;--tw-prose-captions:#6b7280;--tw-prose-kbd:#111827;--tw-prose-kbd-shadows:17 24 39;--tw-prose-code:#111827;--tw-prose-pre-code:#e5e7eb;--tw-prose-pre-bg:#1f2937;--tw-prose-th-borders:#d1d5db;--tw-prose-td-borders:#e5e7eb;--tw-prose-invert-body:#d1d5db;--tw-prose-invert-headings:#fff;--tw-prose-invert-lead:#9ca3af;--tw-prose-invert-links:#fff;--tw-prose-invert-bold:#fff;--tw-prose-invert-counters:#9ca3af;--tw-prose-invert-bullets:#4b5563;--tw-prose-invert-hr:#374151;--tw-prose-invert-quotes:#f3f4f6;--tw-prose-invert-quote-borders:#374151;--tw-prose-invert-captions:#9ca3af;--tw-prose-invert-kbd:#fff;--tw-prose-invert-kbd-shadows:255 255 255;--tw-prose-invert-code:#fff;--tw-prose-invert-pre-code:#d1d5db;--tw-prose-invert-pre-bg:rgba(0,0,0,.5);--tw-prose-invert-th-borders:#4b5563;--tw-prose-invert-td-borders:#374151;font-size:1rem;line-height:1.75}.prose :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}
body td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.5714286em;padding-inline-end:.5714286em;padding-bottom:.5714286em;padding-inline-start:.5714286em}
body td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}
body td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}
body td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.8888889em;padding-inline-end:.6666667em;padding-bottom:.8888889em;padding-inline-start:.6666667em}
body{font-size:1rem;line-height:1.6}
.text-hero{font-weight:700;font-family:var(--font-barlow),sans-serif}
.text-hero{font-size:4rem;line-height:1.1;letter-spacing:-.02em}
.btn-primary{border-radius:1rem;border-width:2px;--tw-border-opacity:1;border-color:rgb(0 0 0/var(--tw-border-opacity,1));background-color:transparent;padding:.875rem 1.5rem;font-weight:700;text-transform:uppercase;letter-spacing:.1em;--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}
.btn-primary:hover{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1));--tw-text-opacity:1}
.btn-red{border-radius:1rem;border-width:2px;padding:.875rem 1.5rem;font-weight:700;text-transform:uppercase;letter-spacing:.1em;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s;--tw-border-opacity:1;border-color:rgb(233 68 54/var(--tw-border-opacity,1));--tw-bg-opacity:1;background-color:rgb(233 68 54/var(--tw-bg-opacity,1));--tw-text-opacity:1}
.btn-red:hover{background-color:transparent;--tw-text-opacity:1;color:rgb(233 68 54/var(--tw-text-opacity,1))}
.flex{display:flex}
.flex-1{flex:1 1 0%}
.flex-shrink-0{flex-shrink:0}
.grid{display:grid}
h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}
h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}
h1,h2,h3,h4,h5,h6{font-weight:700;font-family:var(--font-barlow),sans-serif}
h1):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);font-weight:800;font-size:2.25em;margin-top:0;margin-bottom:.8888889em;line-height:1.1111111}
h1 strong):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:900;color:inherit}
h2):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);font-weight:700;font-size:1.5em;margin-top:2em;margin-bottom:1em;line-height:1.3333333}
h2 strong):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:800;color:inherit}
h3):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);font-weight:600;font-size:1.25em;margin-top:1.6em;margin-bottom:.6em;line-height:1.6}
h3 strong):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:700;color:inherit}
h4):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);font-weight:600;margin-top:1.5em;margin-bottom:.5em;line-height:1.5}
h4 strong):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:700;color:inherit}
h1 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}
h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.875em}
h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.9em}
h4 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}
h2+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}
h3+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}
h4+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}
h1):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:2.8em;margin-top:0;margin-bottom:.8571429em;line-height:1}
h2):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:1.8em;margin-top:1.5555556em;margin-bottom:.8888889em;line-height:1.1111111}
h3):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:1.5em;margin-top:1.6em;margin-bottom:.6666667em;line-height:1.3333333}
h4):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.8em;margin-bottom:.6em;line-height:1.6}
h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.8611111em}
h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.9em}
h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}
h1\:text-4xl :is(:where(h1):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:2.25rem;line-height:2.5rem}
h1\:leading-tight :is(:where(h1):not(:where([class~=not-prose],[class~=not-prose] *))){line-height:1.25}
h2\:mb-6 :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){margin-bottom:1.5rem}
h2\:mt-10 :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:2.5rem}
h2\:border-b :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){border-bottom-width:1px}
h2\:border-bauhaus-black :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){--tw-border-opacity:1;border-color:rgb(0 0 0/var(--tw-border-opacity,1))}
h2\:text-3xl :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:1.875rem;line-height:2.25rem}
h2\:leading-tight :is(:where(h2):not(:where([class~=not-prose],[class~=not-prose] *))){line-height:1.25}
h3\:mb-4 :is(:where(h3):not(:where([class~=not-prose],[class~=not-prose] *))){margin-bottom:1rem}
h3\:mt-8 :is(:where(h3):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:2rem}
h3\:text-2xl :is(:where(h3):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:1.5rem;line-height:2rem}
h3\:leading-snug :is(:where(h3):not(:where([class~=not-prose],[class~=not-prose] *))){line-height:1.375}
h4\:mb-3 :is(:where(h4):not(:where([class~=not-prose],[class~=not-prose] *))){margin-bottom:.75rem}
h4\:mt-6 :is(:where(h4):not(:where([class~=not-prose],[class~=not-prose] *))){margin-top:1.5rem}
h4\:text-xl :is(:where(h4):not(:where([class~=not-prose],[class~=not-prose] *))){font-size:1.25rem;line-height:1.75rem}
h4\:leading-snug :is(:where(h4):not(:where([class~=not-prose],[class~=not-prose] *))){line-height:1.375}
.text-center{text-align:center}
.text-right{text-align:right}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-3xl{font-size:1.875rem;line-height:2.25rem}
.text-4xl{font-size:2.25rem;line-height:2.5rem}
.text-5xl{font-size:3rem;line-height:1}
.text-6xl{font-size:3.75rem;line-height:1}
.text-8xl{font-size:6rem;line-height:1}
.text-base{font-size:1rem;line-height:1.5rem}
.text-body{font-size:1rem;line-height:1.6}
.text-display{font-size:3rem;line-height:1.2;letter-spacing:-.01em}
.text-heading{font-size:2rem;line-height:1.3}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.text-sm{font-size:.875rem;line-height:1.25rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-xs{font-size:.75rem;line-height:1rem}
.text-bauhaus-black{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}
.text-bauhaus-blue{--tw-text-opacity:1;color:rgb(67 72 151/var(--tw-text-opacity,1))}
.text-bauhaus-red{--tw-text-opacity:1;color:rgb(233 68 54/var(--tw-text-opacity,1))}
.text-bauhaus-white{--tw-text-opacity:1;color:rgb(240 235 222/var(--tw-text-opacity,1))}
.text-bauhaus-yellow{--tw-text-opacity:1;color:rgb(255 197 39/var(--tw-text-opacity,1))}
.text-black{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}
.text-blue-100{--tw-text-opacity:1;color:rgb(219 234 254/var(--tw-text-opacity,1))}
.text-brand-background{--tw-text-opacity:1;color:rgb(240 235 222/var(--tw-text-opacity,1))}
.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219/var(--tw-text-opacity,1))}
.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}
.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity,1))}
.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}
.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity,1))}
.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55/var(--tw-text-opacity,1))}
.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}
.text-green-400{--tw-text-opacity:1;color:rgb(74 222 128/var(--tw-text-opacity,1))}
.text-green-700{--tw-text-opacity:1;color:rgb(21 128 61/var(--tw-text-opacity,1))}
.text-orange-400{--tw-text-opacity:1;color:rgb(251 146 60/var(--tw-text-opacity,1))}
.text-red-100{--tw-text-opacity:1;color:rgb(254 226 226/var(--tw-text-opacity,1))}
.text-red-400{--tw-text-opacity:1;color:rgb(248 113 113/var(--tw-text-opacity,1))}
.text-red-700{--tw-text-opacity:1;color:rgb(185 28 28/var(--tw-text-opacity,1))}
.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}
.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}
.font-bold{font-weight:700}
.font-light{font-weight:300}
.font-medium{font-weight:500}
.bg-bauhaus-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1))}
.bg-bauhaus-blue{--tw-bg-opacity:1;background-color:rgb(67 72 151/var(--tw-bg-opacity,1))}
.bg-bauhaus-red{--tw-bg-opacity:1;background-color:rgb(233 68 54/var(--tw-bg-opacity,1))}
.bg-bauhaus-white{--tw-bg-opacity:1;background-color:rgb(240 235 222/var(--tw-bg-opacity,1))}
.bg-bauhaus-yellow{--tw-bg-opacity:1;background-color:rgb(255 197 39/var(--tw-bg-opacity,1))}
.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1))}
.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(37 99 235/var(--tw-bg-opacity,1))}
.bg-brand-background{--tw-bg-opacity:1;background-color:rgb(240 235 222/var(--tw-bg-opacity,1))}
.bg-brand-blue{--tw-bg-opacity:1;background-color:rgb(67 72 151/var(--tw-bg-opacity,1))}
.bg-brand-red{--tw-bg-opacity:1;background-color:rgb(233 68 54/var(--tw-bg-opacity,1))}
.bg-brand-yellow{--tw-bg-opacity:1;background-color:rgb(255 197 39/var(--tw-bg-opacity,1))}
.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity,1))}
.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}
.bg-green-100{--tw-bg-opacity:1;background-color:rgb(220 252 231/var(--tw-bg-opacity,1))}
.bg-red-100{--tw-bg-opacity:1;background-color:rgb(254 226 226/var(--tw-bg-opacity,1))}
.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}
