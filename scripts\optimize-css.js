#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const postcss = require('postcss');
const tailwindcss = require('tailwindcss');
const autoprefixer = require('autoprefixer');

// Critical CSS content - above the fold styles
const criticalCSS = `
/* Critical CSS - Above the fold content only */
*,::before,::after{box-sizing:border-box;border:0 solid #e5e7eb}
html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:var(--font-barlow),sans-serif;overflow-x:hidden}
body{margin:0;line-height:inherit;color:#000;background-color:#f0ebde;overflow-x:hidden;font-family:var(--font-barlow),sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;line-height:1.6}

/* Critical hero section styles */
.hero-section{min-height:100vh;display:flex;align-items:center;justify-content:center;padding:80px 2rem 2rem;position:relative}
.text-hero{font-size:4rem;line-height:1.1;letter-spacing:-0.02em;font-weight:700;font-family:var(--font-barlow),sans-serif;contain:layout style paint;margin:0;text-align:center;max-width:1200px}

/* Critical navigation styles */
.nav-critical{position:fixed;top:0;left:0;right:0;z-index:50;background-color:#f0ebde;border-bottom:1px solid #e5e7eb;padding:0 2rem;height:4rem;display:flex;align-items:center;justify-content:space-between}
.nav-menu{display:flex;gap:2rem;align-items:center}
.nav-link{color:#000;text-decoration:none;font-weight:500;transition:color 0.2s ease}
.nav-link:hover{color:#434897}

/* Critical button styles */
.btn-primary{display:inline-block;padding:0.875rem 1.5rem;border:2px solid #000;background-color:transparent;color:#000;font-weight:700;text-transform:uppercase;letter-spacing:0.05em;text-decoration:none;border-radius:1rem;transition:all 0.2s ease;cursor:pointer;font-family:inherit}
.btn-primary:hover{background-color:#000;color:#f0ebde}

/* Critical layout utilities */
.container{max-width:80rem;margin:0 auto;padding:0 1rem}
.flex{display:flex}
.items-center{align-items:center}
.justify-center{justify-content:center}
.justify-between{justify-content:space-between}
.text-center{text-align:center}
.font-bold{font-weight:700}
.uppercase{text-transform:uppercase}
.tracking-wide{letter-spacing:0.05em}

/* Critical geometric shapes for hero */
.hero-shape{position:absolute;pointer-events:none;opacity:0.8}
.hero-shape-circle{border-radius:50%;background-color:#434897}
.hero-shape-square{background-color:#ffc527}
.hero-shape-triangle{width:0;height:0;background-color:transparent;border-style:solid;border-color:#e94436 transparent transparent transparent}

/* Critical responsive styles */
@media (max-width: 768px){
  .text-hero{font-size:2.5rem}
  .nav-critical{padding:0 1rem}
  .nav-menu{gap:1rem}
  .hero-section{padding:80px 1rem 2rem}
}

@media (max-width: 640px){
  .text-hero{font-size:2rem}
  .nav-menu{display:none}
}

/* Prevent layout shift */
.loading-placeholder{opacity:0;animation:fadeIn 0.3s ease-in-out forwards}
@keyframes fadeIn{to{opacity:1}}

/* Hide non-critical content initially */
.defer-load{opacity:0;transform:translateY(20px);transition:opacity 0.3s ease,transform 0.3s ease}
.defer-load.loaded{opacity:1;transform:translateY(0)}
`;

async function generateOptimizedCSS() {
  try {
    console.log('🎨 Generating optimized CSS files...');

    // Ensure directories exist
    const publicStylesDir = path.join(process.cwd(), 'public', 'styles');
    if (!fs.existsSync(publicStylesDir)) {
      fs.mkdirSync(publicStylesDir, { recursive: true });
    }

    // Process critical CSS
    const criticalResult = await postcss([
      autoprefixer
    ]).process(criticalCSS, { from: undefined });

    // Write critical CSS
    fs.writeFileSync(
      path.join(publicStylesDir, 'critical.css'),
      criticalResult.css
    );

    console.log('✅ Critical CSS generated successfully');

    // Generate Tailwind CSS for non-critical styles
    const tailwindConfig = require('../tailwind.config.ts');
    const globalCSS = fs.readFileSync(
      path.join(process.cwd(), 'src', 'app', 'globals.css'),
      'utf8'
    );

    const nonCriticalResult = await postcss([
      tailwindcss(tailwindConfig),
      autoprefixer
    ]).process(globalCSS, { 
      from: path.join(process.cwd(), 'src', 'app', 'globals.css')
    });

    // Write non-critical CSS (this will be the full Tailwind build)
    fs.writeFileSync(
      path.join(publicStylesDir, 'main.css'),
      nonCriticalResult.css
    );

    console.log('✅ Main CSS generated successfully');

    // Generate file sizes report
    const criticalSize = fs.statSync(path.join(publicStylesDir, 'critical.css')).size;
    const mainSize = fs.statSync(path.join(publicStylesDir, 'main.css')).size;

    console.log('\n📊 CSS File Sizes:');
    console.log(`Critical CSS: ${(criticalSize / 1024).toFixed(2)} KB`);
    console.log(`Main CSS: ${(mainSize / 1024).toFixed(2)} KB`);
    console.log(`Total: ${((criticalSize + mainSize) / 1024).toFixed(2)} KB`);

    console.log('\n🚀 CSS optimization complete!');
    console.log('💡 Critical CSS will be inlined, main CSS will load asynchronously');

  } catch (error) {
    console.error('❌ Error generating CSS:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  generateOptimizedCSS();
}

module.exports = { generateOptimizedCSS };
