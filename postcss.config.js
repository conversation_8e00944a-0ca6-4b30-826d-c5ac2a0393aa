module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    // Only use PurgeCSS in production
    ...(process.env.NODE_ENV === 'production' && {
      '@fullhuman/postcss-purgecss': {
        content: [
          './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
          './src/components/**/*.{js,ts,jsx,tsx,mdx}',
          './src/app/**/*.{js,ts,jsx,tsx,mdx}',
        ],
        defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
        safelist: [
          // Preserve critical classes
          'text-hero',
          'hero-section',
          'nav-critical',
          'btn-primary',
          // Preserve animation classes
          /^animate-/,
          /^scroll-/,
          /^defer-/,
          // Preserve Bauhaus colors
          /^bauhaus-/,
          /^brand-/,
          // Preserve responsive classes
          /^sm:/,
          /^md:/,
          /^lg:/,
          /^xl:/,
          /^2xl:/,
        ],
      },
    }),
  },
}
