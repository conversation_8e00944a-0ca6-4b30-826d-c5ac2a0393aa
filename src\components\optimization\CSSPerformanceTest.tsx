'use client'

import { useEffect, useState } from 'react'

interface PerformanceMetrics {
  criticalCSSSize: number
  mainCSSLoaded: boolean
  mainCSSSize: number
  renderTime: number
  firstPaintTime: number
}

export default function CSSPerformanceTest() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const measurePerformance = async () => {
      try {
        // Measure critical CSS size (inlined)
        const inlineStyles = document.querySelectorAll('style')
        let criticalCSSSize = 0
        inlineStyles.forEach(style => {
          if (style.innerHTML.includes('Critical CSS')) {
            criticalCSSSize = style.innerHTML.length
          }
        })

        // Check if main CSS is loaded
        const mainCSSLink = document.querySelector('link[href="/styles/main.css"]')
        const mainCSSLoaded = !!mainCSSLink

        // Estimate main CSS size
        let mainCSSSize = 0
        if (mainCSSLoaded) {
          try {
            const response = await fetch('/styles/main.css')
            const cssText = await response.text()
            mainCSSSize = cssText.length
          } catch (error) {
            console.warn('Could not fetch main CSS for size measurement')
          }
        }

        // Get performance timing
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const renderTime = perfData.domContentLoadedEventEnd - perfData.navigationStart
        
        // Get paint timing
        const paintEntries = performance.getEntriesByType('paint')
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
        const firstPaintTime = firstPaint ? firstPaint.startTime : 0

        setMetrics({
          criticalCSSSize,
          mainCSSLoaded,
          mainCSSSize,
          renderTime,
          firstPaintTime
        })
      } catch (error) {
        console.error('Error measuring CSS performance:', error)
      }
    }

    // Measure after a short delay to ensure everything is loaded
    const timer = setTimeout(measurePerformance, 1000)
    return () => clearTimeout(timer)
  }, [])

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development')
  }, [])

  if (!isVisible || !metrics) return null

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs font-mono z-50 max-w-xs">
      <div className="font-bold mb-2">CSS Performance Metrics</div>
      <div className="space-y-1">
        <div>Critical CSS: {(metrics.criticalCSSSize / 1024).toFixed(2)} KB (inlined)</div>
        <div>Main CSS: {metrics.mainCSSLoaded ? 'Loaded' : 'Not loaded'}</div>
        {metrics.mainCSSLoaded && (
          <div>Main CSS Size: {(metrics.mainCSSSize / 1024).toFixed(2)} KB</div>
        )}
        <div>Render Time: {metrics.renderTime.toFixed(0)}ms</div>
        <div>First Paint: {metrics.firstPaintTime.toFixed(0)}ms</div>
        <div className="pt-2 text-green-400">
          ✅ No render blocking CSS
        </div>
      </div>
    </div>
  )
}
