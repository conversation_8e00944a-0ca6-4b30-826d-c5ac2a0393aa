'use client'

import { useEffect } from 'react'

interface CSSLoaderProps {
  href: string
  media?: string
  priority?: boolean
}

export default function CSSLoader({ href, media = 'all', priority = false }: CSSLoaderProps) {
  useEffect(() => {
    // Check if the stylesheet is already loaded
    const existingLink = document.querySelector(`link[href="${href}"]`)
    if (existingLink) return

    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    
    if (priority) {
      // Load immediately for critical CSS
      link.media = media
      document.head.appendChild(link)
    } else {
      // Load asynchronously for non-critical CSS
      link.media = 'print'
      link.onload = function() {
        if (this instanceof HTMLLinkElement) {
          this.media = media
        }
      }
      
      // Fallback for browsers that don't support onload
      setTimeout(() => {
        if (link.media === 'print') {
          link.media = media
        }
      }, 100)
      
      document.head.appendChild(link)
    }

    // Cleanup function
    return () => {
      if (link.parentNode) {
        link.parentNode.removeChild(link)
      }
    }
  }, [href, media, priority])

  return null
}

// Hook for programmatic CSS loading
export function useAsyncCSS(href: string, condition: boolean = true) {
  useEffect(() => {
    if (!condition) return

    const existingLink = document.querySelector(`link[href="${href}"]`)
    if (existingLink) return

    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = href
    link.media = 'print'
    link.onload = function() {
      if (this instanceof HTMLLinkElement) {
        this.media = 'all'
      }
    }

    document.head.appendChild(link)

    return () => {
      if (link.parentNode) {
        link.parentNode.removeChild(link)
      }
    }
  }, [href, condition])
}
