'use client'

import { useEffect } from 'react'

interface FontPreloaderProps {
  fonts: Array<{
    href: string
    type?: string
    crossOrigin?: string
  }>
}

export default function FontPreloader({ fonts }: FontPreloaderProps) {
  useEffect(() => {
    fonts.forEach(font => {
      // Check if font is already preloaded
      const existingLink = document.querySelector(`link[href="${font.href}"]`)
      if (existingLink) return

      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.type = font.type || 'font/woff2'
      link.href = font.href
      link.crossOrigin = font.crossOrigin || 'anonymous'
      
      document.head.appendChild(link)
    })
  }, [fonts])

  return null
}

// Hook for programmatic font preloading
export function useFontPreload(fontUrl: string, condition: boolean = true) {
  useEffect(() => {
    if (!condition) return

    const existingLink = document.querySelector(`link[href="${fontUrl}"]`)
    if (existingLink) return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff2'
    link.href = fontUrl
    link.crossOrigin = 'anonymous'
    
    document.head.appendChild(link)

    return () => {
      if (link.parentNode) {
        link.parentNode.removeChild(link)
      }
    }
  }, [fontUrl, condition])
}
