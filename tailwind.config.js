/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'barlow': ['var(--font-barlow)', 'sans-serif'],
        'sans': ['var(--font-barlow)', 'sans-serif'],
      },
      colors: {
        // Refined Bauhaus color palette
        'bauhaus': {
          'red': '#e94436',
          'yellow': '#ffc527',
          'blue': '#434897',
          'black': '#000000',
          'white': '#f0ebde',
        },
        // Brand colors for components
        'brand': {
          'red': '#e94436',
          'yellow': '#ffc527',
          'blue': '#434897',
          'background': '#f0ebde',
        }
      },
      fontSize: {
        'hero': ['4rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'display': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
        'heading': ['2rem', { lineHeight: '1.3' }],
        'subheading': ['1.5rem', { lineHeight: '1.4' }],
        'body': ['1rem', { lineHeight: '1.6' }],
        'small': ['0.875rem', { lineHeight: '1.5' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      letterSpacing: {
        'tighter': '-0.02em',
        'tight': '-0.01em',
        'wide': '0.1em',
        'wider': '0.2em',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
